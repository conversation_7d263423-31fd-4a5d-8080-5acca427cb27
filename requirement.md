# 苹果计算器需求文档（纯前端实现）

## 1. 概述
基于原生HTML/CSS/JavaScript技术栈，完整复现iOS系统计算器的核心功能与交互体验，支持桌面端和移动端浏览器运行。

## 2. 功能需求

### 2.1 核心计算功能
| 功能模块        | 详细说明                                                                 |
|-----------------|--------------------------------------------------------------------------|
| 基础运算        | 支持加减乘除四则运算，优先级计算（如：2+3×5=17）                        |
| 连续运算        | 支持链式运算（如：10+5=15→×2=30→-3=27）                                 |
| 正负转换        | ±按钮可切换当前数值正负号                                               |
| 百分比运算      | 将当前数值转换为百分比值（如：200%→2）                                  |
| 小数运算        | 支持最多15位小数显示，自动四舍五入处理                                  |
| 清除功能        | C按钮清空所有输入，CE按钮清除当前输入                                   |

### 2.2 界面设计规范
```html
<!-- 结构示意 -->
<div class="calculator">
  <div class="display">0</div>
  <div class="buttons">
    <button class="func">AC</button>
    <button class="func">±</button>
    <button class="func">%</button>
    <button class="operator">÷</button>
    <!-- ...其他按钮 -->
  </div>
</div>
```
**视觉要求：**
- 配色方案：iOS深色模式（#333333背景，橙色操作符按钮）
- 字体规范：San Francisco字体优先，备用Helvetica Neue
- 按钮尺寸：桌面端80×80px，移动端自适应
- 动态效果：按钮按下态缩放（transform: scale(0.95)）

### 2.3 交互规范
1. 数字输入：
   - 输入长度限制：最大15位数字（含小数点）
   - 数字格式化：自动添加千位分隔符（1,000.5）
   - 小数处理：禁止重复输入小数点

2. 运算反馈：
   - 实时显示计算过程（如：12 + 3 → = → 15）
   - 错误处理：除以零显示"Error"，需按C重置
   - 溢出处理：科学计数法显示（如：1.23456789e+12）

3. 动画效果：
   - 按钮点击波纹动画
   - 显示区域数字滚动过渡

## 3. 用户场景

### 3.1 典型使用场景
1. **日常计算场景**  
用户需要快速计算购物清单总价，连续输入：  
`15.99 + 4.5 × 2 =` → 显示24.99

2. **账单分摊场景**  
计算聚餐费用分摊：  
`198 ÷ 3 =` → 显示66，随后点击±显示-66

3. **百分比折扣计算**  
计算商品折扣价：  
`200 × 25% =` → 显示50

4. **复杂表达式计算**  
工程计算需求：  
`(3 + 5) × 2² ÷ 4 =` → 需分步输入实现

## 4. 技术实现方案

### 4.1 架构设计
```mermaid
graph TD
  A[事件监听] --> B[输入验证]
  B --> C{操作类型判断}
  C -->|数字| D[更新显示值]
  C -->|操作符| E[保存当前运算]
  C -->|等号| F[执行计算]
  F --> G[显示结果]
```

### 4.2 关键技术点
1. **计算逻辑实现**
   - 使用JavaScript的`eval()`替代方案
   - 自定义运算栈处理优先级
   ```javascript
   class Calculator {
     constructor() {
       this.currentValue = '0';
       this.pendingOperation = null;
       this.memoryValue = null;
     }
     // 运算方法实现...
   }
   ```

2. **响应式布局**
   ```css
   @media (max-width: 600px) {
     .calculator {
       width: 100vw;
       height: 100vh;
     }
   }
   ```

3. **性能优化**
   - Web Worker处理复杂计算
   - 防抖处理连续点击事件

## 5. 测试计划

### 5.1 单元测试用例
| 测试案例          | 输入序列         | 预期输出  |
|-------------------|------------------|-----------|
| 基本加法          | 2 + 3 =          | 5         |
| 运算优先级        | 2 + 3 × 4 =      | 14        |
| 连续运算          | 10 + 5 = × 2 =   | 30        |
| 边界值处理        | 9999999999999999 | 科学计数法|

### 5.2 跨平台测试矩阵
| 平台/浏览器       | Chrome | Safari | Firefox |
|-------------------|--------|--------|---------|
| iOS 15+           | ✔      | ✔      | ✔       |
| Android 10+       | ✔      | ✔      | ✔       |
| Windows 10        | ✔      | N/A    | ✔       |
| macOS Monterey    | ✔      | ✔      | ✔       |

## 6. 项目交付物
1. 完整源代码（含注释）
2. 响应式部署方案
3. 性能测试报告
4. 用户操作手册（含快捷键说明）

## 附录：特殊符号处理逻辑
| 输入状态       | 处理规则                         |
|----------------|----------------------------------|
| 初始零状态     | 输入数字时替换显示（0→5）       |
| 运算后状态     | 新输入数字重置显示              |
| 错误状态       | 必须按C清除后才能继续操作       |

---

**版本历史**  
v1.0 - 2024-03-15 - 初始版本